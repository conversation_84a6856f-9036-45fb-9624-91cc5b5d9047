.demo-section {
    margin: 2rem 0;
    padding: 1.5rem;
    border: 1px solid var(--color-base-100);
    border-radius: 0.5rem;
    background-color: var(--color-base-100);
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
    background-color: var(--color-base-100);
}

.demo-item {
    text-align: center;
    background-color: var(--color-base-200);
    padding: 1rem;
    border-radius: 0.375rem;
  }

.demo-item h4 {
    margin: 0 0 1rem 0;
    color: #1e293b;
    font-size: 0.825rem;
    font-weight: 600;
}  

.demo-item-gray {
    text-align: center;
    padding: 1rem;
    background-color: #6b7280;
    padding: 1rem;
    border-radius: 0.375rem;
}

.demo-item-gray h4 {
    margin: 0 0 1rem 0;
    color: #1e293b;
    font-size: 0.825rem;
    font-weight: 600;
}

/* Force mobile logo variants to display regardless of viewport size */
.force-mobile .logo {
  /* Override desktop mask assets with mobile ones */
}

[data-theme="ga"] .force-mobile .logo {
  -webkit-mask-image: url('/assets/ga/logo-mobile-ga-black.svg') !important;
  mask-image: url('/assets/ga/logo-mobile-ga-black.svg') !important;
  aspect-ratio: 212.2 / 134.21 !important;
}

[data-theme="la-slc"] .force-mobile .logo {
  -webkit-mask-image: url('/assets/la-slc/logo-mobile-la-slc-black.svg') !important;
  mask-image: url('/assets/la-slc/logo-mobile-la-slc-black.svg') !important;
  aspect-ratio: 202.83 / 96.03 !important;
}

/* Center logos within demo item containers */
.demo-item > div,
.demo-item-gray > div,
.demo-item-dark > div {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

/* Ensure logo components are centered */
.demo-item gahr-logo,
.demo-item-gray gahr-logo,
.demo-item-dark gahr-logo {
  display: block;
  margin: 0 auto;
}

/* Enhanced vertical centering for demo items */
.demo-item,
.demo-item-gray,
.demo-item-dark {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 120px; /* Ensure consistent height for better centering */
}

/* Ensure the content within demo items is properly centered */
.demo-item > *,
.demo-item-gray > *,
.demo-item-dark > * {
  text-align: center;
}