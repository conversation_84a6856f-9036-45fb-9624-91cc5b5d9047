name: Publish Design System Package

on:
  workflow_dispatch:
    inputs:
      dry_run:
        description: 'Run in dry-run mode (build only, no publish)'
        required: false
        default: false
        type: boolean

jobs:
  publish:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@gahr'
          
      - name: Get package version
        id: package-version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Package version: $VERSION"
          
      - name: Check if version already exists
        id: version-check
        run: |
          PACKAGE_NAME="@gahr/gahr-design-system"
          VERSION="${{ steps.package-version.outputs.version }}"
          
          # Check if version exists in GitHub Package Registry
          if npm view "$PACKAGE_NAME@$VERSION" --registry=https://npm.pkg.github.com 2>/dev/null; then
            echo "exists=true" >> $GITHUB_OUTPUT
            echo "Version $VERSION already exists in the registry"
          else
            echo "exists=false" >> $GITHUB_OUTPUT
            echo "Version $VERSION does not exist in the registry"
          fi
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Fail if version exists
        if: steps.version-check.outputs.exists == 'true'
        run: |
          echo "❌ Version ${{ steps.package-version.outputs.version }} already exists in the GitHub Package Registry"
          echo "Please update the version in package.json before publishing"
          exit 1
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Clean previous builds
        run: npm run clean
        
      - name: Build package
        run: npm run build
        
      - name: Verify build output
        run: |
          echo "Checking build output..."
          ls -la dist/
          echo "Verifying required files exist..."
          test -f dist/index.js || (echo "❌ dist/index.js not found" && exit 1)
          test -f dist/index.cjs.js || (echo "❌ dist/index.cjs.js not found" && exit 1)
          test -d dist/types || (echo "❌ dist/types directory not found" && exit 1)
          test -d dist/react || (echo "❌ dist/react directory not found" && exit 1)
          echo "✅ All required build files are present"
          
      - name: Update package.json for GitHub Package Registry
        run: |
          # Ensure the package name is scoped correctly for GitHub Package Registry
          node -e "
            const fs = require('fs');
            const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            
            // Update repository URL to point to the actual GAHR repository
            pkg.repository = {
              type: 'git',
              url: 'git+https://github.com/GAHR-martech/gahr-design-system.git'
            };
            
            // Add publishConfig for GitHub Package Registry
            pkg.publishConfig = {
              registry: 'https://npm.pkg.github.com',
              access: 'restricted'
            };
            
            fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
          "
          
      - name: Publish to GitHub Package Registry (Dry Run)
        if: inputs.dry_run == true
        run: |
          echo "🔍 Dry run mode - would publish version ${{ steps.package-version.outputs.version }}"
          npm publish --dry-run --registry=https://npm.pkg.github.com
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Publish to GitHub Package Registry
        if: inputs.dry_run != true
        run: |
          echo "📦 Publishing version ${{ steps.package-version.outputs.version }} to GitHub Package Registry"
          npm publish --registry=https://npm.pkg.github.com
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Create release summary
        if: inputs.dry_run != true
        run: |
          echo "## 🎉 Package Published Successfully" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Package:** @gahr/gahr-design-system" >> $GITHUB_STEP_SUMMARY
          echo "**Version:** ${{ steps.package-version.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "**Registry:** GitHub Package Registry" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Installation" >> $GITHUB_STEP_SUMMARY
          echo '```bash' >> $GITHUB_STEP_SUMMARY
          echo "npm install @gahr/gahr-design-system@${{ steps.package-version.outputs.version }} --registry=https://npm.pkg.github.com" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- Update your web project to use the new version" >> $GITHUB_STEP_SUMMARY
          echo "- Test the integration in your development environment" >> $GITHUB_STEP_SUMMARY
          echo "- Consider updating documentation if there are breaking changes" >> $GITHUB_STEP_SUMMARY
