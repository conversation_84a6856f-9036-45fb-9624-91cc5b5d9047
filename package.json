{"name": "@gahr/gahr-design-system", "version": "0.0.1", "description": "GAHR Design System", "main": "dist/index.cjs.js", "module": "dist/index.js", "types": "dist/types/index.d.ts", "collection": "dist/collection/collection-manifest.json", "collection:main": "dist/collection/index.js", "unpkg": "dist/gahr/gahr.esm.js", "exports": {".": {"import": "./dist/gahr/gahr.esm.js", "require": "./dist/gahr/gahr.cjs.js"}, "./hydrate": {"import": "./dist/hydrate/index.mjs", "require": "./dist/hydrate/index.js", "types": "./dist/hydrate/index.d.ts"}, "./loader": {"import": "./dist/loader/index.js", "require": "./dist/loader/index.cjs", "types": "./dist/loader/index.d.ts"}, "./react": {"import": "./dist/react/components.js", "types": "./dist/react/components.d.ts"}}, "repository": {"type": "git", "url": "git+https://github.com/GAHR-martech/gahr-design-system.git"}, "publishConfig": {"registry": "https://npm.pkg.github.com", "access": "restricted"}, "files": ["dist/"], "scripts": {"build": "npm run build.src && npm run build.react", "build.src": "stencil build", "build.react": "tsc -p ./tsconfig.react.json", "start": "stencil build --dev --watch --serve", "test": "stencil test --spec --e2e", "test.watch": "stencil test --spec --e2e --watchAll", "generate": "stencil generate", "clean": "rimraf dist www", "test-package": "mkdirp test-packages && npm pack --pack-destination test-packages", "version:patch": "npm version patch", "version:minor": "npm version minor", "version:major": "npm version major", "prepublishOnly": "npm run clean && npm run build && npm test"}, "dependencies": {"@stencil/react-output-target": "^1.2.0", "@stencil/ssr": "^0.1.1", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@stencil/core": "^4.27.1", "@tailwindcss/typography": "^0.5.16", "@types/jest": "^29.5.14", "@types/node": "^22.13.5", "@types/react": "^19.1.9", "daisyui": "^5.0.48", "jest": "^29.7.0", "jest-cli": "^29.7.0", "mkdirp": "^3.0.1", "puppeteer": "^24.3.0", "rimraf": "^6.0.1", "stencil-tailwind-plugin": "^2.0.5", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "license": "MIT"}